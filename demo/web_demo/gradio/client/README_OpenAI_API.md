# MiniCPM-V 4.5 Gradio Client with OpenAI API

这个版本的Gradio客户端已经修改为使用OpenAI Python库来与vLLM服务器通信，而不是直接使用requests库。

## 配置说明

### 1. 启动vLLM服务器

在运行客户端之前，需要先启动vLLM服务器：

```bash
vllm serve <model_path> --dtype auto --max-model-len 2048 --api-key token-abc123 --gpu_memory_utilization 0.9 --trust-remote-code
```

### 2. 配置客户端

在 `gradio_client_minicpmv4_5.py` 文件中修改以下配置：

```python
# OpenAI API configuration
openai_api_key = "token-abc123"  # 必须与启动服务时的API密钥匹配
openai_api_base = "http://localhost:8000/v1"  # vLLM服务器地址
model_path = "<model_path>"  # 指定模型路径或HuggingFace ID
```

### 3. 主要变化

- **移除了requests库依赖**：现在使用OpenAI Python库
- **符合OpenAI API规范**：请求和响应格式遵循OpenAI标准
- **支持思考模式**：通过 `chat_template_kwargs` 参数控制
- **流式输出**：使用OpenAI的流式API
- **多模态支持**：图片和视频内容转换为OpenAI格式

### 4. 依赖安装

确保安装了OpenAI Python库：

```bash
pip install openai
```

### 5. 支持的功能

- ✅ 图片推理
- ✅ 视频推理（转换为帧序列）
- ✅ 思考模式
- ✅ 流式输出
- ✅ 多轮对话
- ✅ 停止控制

### 6. 注意事项

1. **API密钥匹配**：客户端的API密钥必须与vLLM服务器启动时设置的密钥一致
2. **模型路径**：将 `<model_path>` 替换为实际的MiniCPM-V 4.5模型路径
3. **服务器地址**：如果vLLM服务器运行在不同的地址或端口，请相应修改 `openai_api_base`
4. **视频处理**：视频文件会被转换为帧序列，然后作为图片序列发送给API

### 7. 错误排查

如果遇到连接问题：
1. 确认vLLM服务器正在运行
2. 检查API密钥是否匹配
3. 验证服务器地址和端口
4. 查看服务器日志获取详细错误信息
