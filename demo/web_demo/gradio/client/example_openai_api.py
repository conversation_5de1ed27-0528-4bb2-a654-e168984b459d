#!/usr/bin/env python
# encoding: utf-8
"""
MiniCPM-V 4.5 OpenAI API 使用示例

这个脚本展示了如何直接使用OpenAI Python库与vLLM服务器进行交互，
而不需要通过Gradio界面。
"""

from openai import OpenAI
import base64
import os

# OpenAI API configuration
openai_api_key = "token-abc123"  # 必须与启动服务时的API密钥匹配
openai_api_base = "http://localhost:8000/v1"
model_path = "<model_path>"  # 替换为实际的模型路径

# Initialize OpenAI client
client = OpenAI(
    api_key=openai_api_key,
    base_url=openai_api_base,
)


def encode_image_to_base64(image_path):
    """将图片文件编码为base64格式"""
    with open(image_path, 'rb') as image_file:
        return base64.b64encode(image_file.read()).decode('utf-8')


def encode_video_to_base64(video_path):
    """将视频文件编码为base64格式"""
    with open(video_path, 'rb') as video_file:
        return base64.b64encode(video_file.read()).decode('utf-8')


def image_inference_example():
    """图片推理示例"""
    print("=== 图片推理示例 ===")
    
    # 替换为实际的图片路径
    image_path = "./assets/airplane.jpeg"
    
    if not os.path.exists(image_path):
        print(f"图片文件不存在: {image_path}")
        return
    
    # 编码图片
    image_base64 = encode_image_to_base64(image_path)
    image_data = f"data:image/jpeg;base64,{image_base64}"
    
    # 发送请求
    chat_response = client.chat.completions.create(
        model=model_path,
        messages=[{
            "role": "user",
            "content": [
                {"type": "text", "text": "请描述这张图片"},
                {
                    "type": "image_url",
                    "image_url": {"url": image_data},
                },
            ],
        }],
        extra_body={
            "stop_token_ids": [1, 73440]
        }
    )
    
    print("回复:", chat_response.choices[0].message.content)


def video_inference_example():
    """视频推理示例"""
    print("\n=== 视频推理示例 ===")
    
    # 替换为实际的视频路径
    video_path = "./videos/video.mp4"
    
    if not os.path.exists(video_path):
        print(f"视频文件不存在: {video_path}")
        return
    
    # 编码视频
    video_base64 = encode_video_to_base64(video_path)
    video_data = f"data:video/mp4;base64,{video_base64}"
    
    # 发送请求
    chat_response = client.chat.completions.create(
        model=model_path,
        messages=[{
            "role": "user",
            "content": [
                {"type": "text", "text": "请描述这个视频"},
                {
                    "type": "video_url",
                    "video_url": {"url": video_data},
                },
            ],
        }],
        extra_body={
            "stop_token_ids": [1, 73440]
        }
    )
    
    print("回复:", chat_response.choices[0].message.content)


def thinking_mode_example():
    """思考模式示例"""
    print("\n=== 思考模式示例 ===")
    
    # 替换为实际的图片路径
    image_path = "./assets/airplane.jpeg"
    
    if not os.path.exists(image_path):
        print(f"图片文件不存在: {image_path}")
        return
    
    # 编码图片
    image_base64 = encode_image_to_base64(image_path)
    image_data = f"data:image/jpeg;base64,{image_base64}"
    
    # 发送请求（启用思考模式）
    chat_response = client.chat.completions.create(
        model=model_path,
        messages=[{
            "role": "user",
            "content": [
                {"type": "text", "text": "请仔细分析这张图片中的所有细节"},
                {
                    "type": "image_url",
                    "image_url": {"url": image_data},
                },
            ],
        }],
        extra_body={
            "stop_token_ids": [1, 73440],
            "chat_template_kwargs": {"enable_thinking": True},
        }
    )
    
    print("回复（包含思考过程）:", chat_response.choices[0].message.content)


def streaming_example():
    """流式输出示例"""
    print("\n=== 流式输出示例 ===")
    
    # 发送流式请求
    stream = client.chat.completions.create(
        model=model_path,
        messages=[{
            "role": "user",
            "content": [
                {"type": "text", "text": "请写一首关于人工智能的诗"},
            ],
        }],
        stream=True,
        extra_body={
            "stop_token_ids": [1, 73440]
        }
    )
    
    print("流式回复:")
    for chunk in stream:
        if chunk.choices[0].delta.content is not None:
            print(chunk.choices[0].delta.content, end='', flush=True)
    print()  # 换行


def multi_turn_conversation_example():
    """多轮对话示例"""
    print("\n=== 多轮对话示例 ===")
    
    messages = [
        {
            "role": "system",
            "content": "你是一个有用的助手。",
        }
    ]
    
    # 第一轮对话
    messages.append({
        "role": "user",
        "content": [
            {"type": "text", "text": "你好，请介绍一下你自己"},
        ],
    })
    
    response = client.chat.completions.create(
        model=model_path,
        messages=messages,
        extra_body={
            "stop_token_ids": [1, 73440]
        }
    )
    
    assistant_reply = response.choices[0].message.content
    print("助手:", assistant_reply)
    
    # 添加助手回复到对话历史
    messages.append({
        "role": "assistant",
        "content": assistant_reply,
    })
    
    # 第二轮对话
    messages.append({
        "role": "user",
        "content": [
            {"type": "text", "text": "你能帮我做什么？"},
        ],
    })
    
    response = client.chat.completions.create(
        model=model_path,
        messages=messages,
        extra_body={
            "stop_token_ids": [1, 73440]
        }
    )
    
    print("助手:", response.choices[0].message.content)


if __name__ == "__main__":
    print("MiniCPM-V 4.5 OpenAI API 使用示例")
    print("请确保vLLM服务器正在运行，并且配置正确。")
    print("启动命令: vllm serve <model_path> --dtype auto --max-model-len 2048 --api-key token-abc123 --gpu_memory_utilization 0.9 --trust-remote-code")
    print()
    
    try:
        # 运行各种示例
        image_inference_example()
        video_inference_example()
        thinking_mode_example()
        streaming_example()
        multi_turn_conversation_example()
        
    except Exception as e:
        print(f"错误: {e}")
        print("请检查vLLM服务器是否正在运行，以及配置是否正确。")
